# Pallet Crate Counting Prototype

## Overview
This project is a **prototype application** designed to help blueberry farms (or any similar agricultural operation) **automatically digitize pallet weighing and crate counting**.  
Traditionally, farms weigh each crate manually, subtract the tare (weight of the crate), and log everything into Excel. This app aims to **automate the process** by:

- Detecting and counting crates on a pallet from a single photo (or two photos: front + side).
- Classifying crate types (e.g., plastic vs. wood).
- Estimating total net, tare, and gross weights based on predefined values.
- Exporting results as structured JSON for integration with databases or ERP systems.

This makes the workflow **faster, less error-prone, and fully digitized**.

---

## Features
- **YOLO-based object detection** for crate detection and classification.
- **ONNX Runtime (DirectML)** support to run locally on Windows laptops (AMD, Intel, or NVIDIA GPUs).
- **Post-processing logic** ("grid snapping") to stabilize counts, even when crates are partially hidden or occluded.
- **JSON export** with:
  - Number of crates by type
  - Estimated net/tare/gross weight
  - Confidence values for detections
  - Links to photos for audit

---

## Example JSON Output
```json
{
  "pallet_id": "PAL-2025-08-31-001",
  "timestamp": "2025-08-31T10:05:33+03:00",
  "detection": {
    "counts": {
      "crate_plastic": 36,
      "crate_wood": 12
    },
    "confidences": {
      "crate_plastic": 0.98,
      "crate_wood": 0.96
    }
  },
  "assumptions": {
    "net_per_crate_kg": 2.00,
    "tare": {
      "crate_plastic_kg": 0.62,
      "crate_wood_kg": 0.85,
      "pallet_euro_kg": 25.0
    }
  },
  "totals": {
    "count_crates": 48,
    "net_kg": 96.0,
    "tare_kg": 57.3,
    "gross_kg": 153.3
  }
}
