#!/usr/bin/env python3
"""
Download and prepare YOLO model for boxread
This script will try to download a YOLOv8 model and export it to ONNX format
"""

import os
import sys

def download_yolo_model():
    """Download and export YOLOv8 model to ONNX format"""
    print("=== YOLO Model Setup ===")
    
    # Check if model already exists
    if os.path.exists("yolov8n.onnx"):
        print("✅ yolov8n.onnx already exists!")
        return True
    
    try:
        # Try to import ultralytics
        from ultralytics import YOLO
        print("✅ Ultralytics imported successfully")
        
        # Load YOLOv8 nano model (smallest, fastest)
        print("📥 Downloading YOLOv8n model...")
        model = YOLO('yolov8n.pt')  # This will download the model if not present
        print("✅ YOLOv8n model loaded")
        
        # Export to ONNX format
        print("🔄 Exporting to ONNX format...")
        model.export(format='onnx', opset=13)
        print("✅ Model exported to yolov8n.onnx")
        
        return True
        
    except ImportError as e:
        print(f"❌ Cannot import ultralytics: {e}")
        print("💡 Try installing Visual C++ Redistributable first:")
        print("   https://aka.ms/vs/16/release/vc_redist.x64.exe")
        return False
        
    except Exception as e:
        print(f"❌ Error setting up model: {e}")
        return False

def alternative_download():
    """Alternative method to get ONNX model directly"""
    print("\n=== Alternative: Direct ONNX Download ===")
    print("If the above method doesn't work, you can:")
    print("1. Download YOLOv8n ONNX model directly from:")
    print("   https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.onnx")
    print("2. Save it as 'yolov8n.onnx' in this directory")
    print("3. Run the test again")

def main():
    """Main function"""
    print("Setting up YOLO model for boxread...\n")
    
    success = download_yolo_model()
    
    if not success:
        alternative_download()
    else:
        print("\n✅ Model setup complete!")
        print("🚀 You can now run the full detection test")

if __name__ == "__main__":
    main()
