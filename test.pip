import onnxruntime as ort, cv2, numpy as np
from ultralytics.utils import ops

# 1) Sesiune ORT cu DirectML
sess = ort.InferenceSession("yolov8n.onnx", providers=["DmlExecutionProvider","CPUExecutionProvider"])
inp_name = sess.get_inputs()[0].name

# 2) Preproc
img = cv2.imread("pallet.jpg")
h0, w0 = img.shape[:2]
sz = 640
img_r = cv2.resize(img, (sz, sz))
x = img_r[:, :, ::-1].transpose(2,0,1)  # BGR->RGB, HWC->CHW
x = np.ascontiguousarray(x, dtype=np.float32) / 255.0
x = x[None, ...]

# 3) Infer
pred = sess.run(None, {inp_name: x})[0]  # [1, N, 85] for YOLOv8
# 4) NMS + rescale (Ultralytics helper)
pred = ops.non_max_suppression_np(pred, conf_thres=0.25, iou_thres=0.45, max_det=300)[0]
pred[:, :4] = ops.scale_boxes((sz, sz), pred[:, :4], (h0, w0))

# 5) Filtrează clasele tale (ex. crate_plastic/wood) și numără
# (presupunând că clasa 0=crate_plastic, 1=crate_wood)
cls = pred[:, 5].astype(int)
count_plastic = int((cls == 0).sum())
count_wood    = int((cls == 1).sum())
print({"crate_plastic": count_plastic, "crate_wood": count_wood})
