#!/usr/bin/env python3
"""
Basic test script for boxread - Pallet Crate Counting
Tests basic image loading and processing without ONNX Runtime
"""

import cv2
import numpy as np
import os

def test_image_loading():
    """Test if we can load and process the test image"""
    print("=== Boxread Basic Test ===")
    
    # Check if test image exists
    image_path = "test.jpeg"
    if not os.path.exists(image_path):
        print(f"❌ Error: {image_path} not found!")
        return False
    
    print(f"✅ Found test image: {image_path}")
    
    # Load the image
    try:
        img = cv2.imread(image_path)
        if img is None:
            print("❌ Error: Could not load image!")
            return False
        
        h, w, c = img.shape
        print(f"✅ Image loaded successfully: {w}x{h} pixels, {c} channels")
        
        # Basic image processing test
        # Convert to RGB (OpenCV loads as BGR)
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        print("✅ Color conversion successful")
        
        # Resize to standard YOLO input size (640x640)
        img_resized = cv2.resize(img_rgb, (640, 640))
        print("✅ Image resize successful")
        
        # Normalize for neural network input
        img_normalized = img_resized.astype(np.float32) / 255.0
        print("✅ Image normalization successful")
        
        # Prepare for batch processing (add batch dimension)
        img_batch = np.expand_dims(img_normalized, axis=0)
        print(f"✅ Batch preparation successful: shape {img_batch.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing image: {e}")
        return False

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n=== Dependency Check ===")
    
    # Test OpenCV
    try:
        print(f"✅ OpenCV version: {cv2.__version__}")
    except Exception as e:
        print(f"❌ OpenCV error: {e}")
        return False
    
    # Test NumPy
    try:
        print(f"✅ NumPy version: {np.__version__}")
    except Exception as e:
        print(f"❌ NumPy error: {e}")
        return False
    
    # Test ONNX Runtime (optional for now)
    try:
        import onnxruntime as ort
        print(f"✅ ONNX Runtime version: {ort.__version__}")
        
        # Test available providers
        providers = ort.get_available_providers()
        print(f"✅ Available providers: {providers}")
        
        if "DmlExecutionProvider" in providers:
            print("✅ DirectML support available!")
        elif "CPUExecutionProvider" in providers:
            print("⚠️  Only CPU provider available (no GPU acceleration)")
        
    except Exception as e:
        print(f"⚠️  ONNX Runtime not available: {e}")
        print("   (This is OK for basic testing)")
    
    # Test Ultralytics
    try:
        from ultralytics.utils import ops
        print("✅ Ultralytics utils available")
    except Exception as e:
        print(f"⚠️  Ultralytics error: {e}")
        print("   (This is needed for YOLO post-processing)")
    
    return True

def main():
    """Run all tests"""
    print("Starting Boxread Test Suite...\n")
    
    # Test dependencies
    deps_ok = test_dependencies()
    
    # Test image processing
    img_ok = test_image_loading()
    
    print("\n=== Test Summary ===")
    if deps_ok and img_ok:
        print("✅ All basic tests passed!")
        print("📋 Next steps:")
        print("   1. Get a YOLO model (yolov8n.onnx)")
        print("   2. Test ONNX Runtime inference")
        print("   3. Implement crate detection and counting")
    else:
        print("❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
