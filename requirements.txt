# Core dependencies for boxread - Pallet Crate Counting Prototype

# YOLO and computer vision
ultralytics>=8.0.0
opencv-python>=4.8.0

# ONNX Runtime with DirectML support for Windows GPU acceleration
onnxruntime-directml>=1.16.0

# Core scientific computing
numpy>=1.24.0

# Optional: For potential future features
# Pillow>=10.0.0  # Alternative image processing
# matplotlib>=3.7.0  # For visualization/debugging
# pandas>=2.0.0  # For data handling if needed
